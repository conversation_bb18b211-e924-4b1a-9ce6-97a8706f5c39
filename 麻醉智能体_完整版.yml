app:
  description: 专业麻醉学智能助手 - 具备论文查阅能力
  icon: 🏥
  icon_background: '#E8F5E8'
  mode: workflow
  name: 麻醉学专家智能体
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/ollama:0.0.6@f430f3eb959f4863b1e87171544a8fec179441b90deda5693c85f07712d2a68c
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      enabled: false
    opening_statement: '🏥 **专业麻醉学智能助手**


      我是您的专业麻醉学顾问，具备以下能力：


      ✨ **循证医学**：实时查阅最新PubMed文献

      🎯 **专业权威**：30年临床经验的麻醉科主任水平

      🔬 **智能分析**：基于大量高质量文献的专业分析

      ⚡ **快速响应**：优化算法，快速给出专业建议


      **核心优势**：

      - 基于最新医学文献的循证建议

      - 达到麻醉科主任认可的专业水平

      - 快速响应，无需等待复杂过程

      - 5-8篇高质量文献支持


      请输入您的麻醉学问题，我将查阅最新文献为您提供专业建议！

      '
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 丙泊酚在老年患者中的剂量调整原则？
    - 困难气道的术前评估要点有哪些？
    - 椎管内麻醉的并发症如何预防？
    - 小儿麻醉的特殊注意事项？
    - 心脏手术麻醉的关键管理要点？
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 5001-source-5002-target
      source: '5001'
      sourceHandle: source
      target: '5002'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: 5002-source-5003-target
      source: '5002'
      sourceHandle: source
      target: '5003'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: 5003-source-5004-target
      source: '5003'
      sourceHandle: source
      target: '5004'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 5004-source-5005-target
      source: '5004'
      sourceHandle: source
      target: '5005'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 麻醉学问题
          max_length: 500
          options: []
          required: true
          type: text-input
          variable: anesthesia_question
      height: 88
      id: '5001'
      position:
        x: 50
        y: 300
      positionAbsolute:
        x: 50
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.2
          mode: chat
          name: deepseek-r1:32B
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: keyword_generation_prompt
          role: system
          text: '# 🔍 精准麻醉学文献搜索关键词生成专家

            ## 用户问题
            {{#5001.anesthesia_question#}}

            ## 核心任务
            **关键词准确性是最重要的！** 必须深度分析用户问题，提取最精确的医学关键词，确保搜索到的文献直接相关。

            ## 深度问题分析框架

            ### 1. 核心医学概念识别
            - **主要疾病/病理**：精确识别疾病名称（如hypertension而非cardiovascular disease）
            - **具体药物/技术**：精确药物名称（如propofol而非anesthetic agent）
            - **特定操作/程序**：精确操作名称（如endotracheal intubation而非airway management）
            - **明确参数/指标**：具体数值范围（如blood pressure control而非hemodynamic management）

            ### 2. 关键修饰词识别
            - **人群特征**：elderly, pediatric, pregnant, obese等
            - **时间阶段**：preoperative, intraoperative, postoperative
            - **严重程度**：severe, moderate, mild
            - **特殊情况**：emergency, elective, high-risk

            ### 3. 避免过度泛化
            ❌ **禁止使用过于宽泛的术语**：
            - 不用"anesthesia"替代具体麻醉方法
            - 不用"surgery"替代具体手术类型
            - 不用"medication"替代具体药物名称
            - 不用"complication"替代具体并发症

            ## 精准搜索策略设计

            ### 主要关键词（PRIMARY）
            - 必须包含问题的**所有核心要素**
            - 使用最精确的医学术语
            - 确保能搜索到直接回答问题的文献
            - 预期结果：5-15篇高度相关文献

            ### 备选关键词（SECONDARY）
            - 保持核心概念，适度调整表达方式
            - 使用同义词或相关术语
            - 仍然针对具体问题，不过度扩展
            - 预期结果：10-25篇相关文献

            ### 扩展关键词（BROAD）
            - 保留最重要的1-2个核心概念
            - 适度扩展搜索范围，但不偏离主题
            - 作为前两层搜索失败时的备选
            - 预期结果：15-30篇文献

            ## 输出格式（严格遵循）

            ### SEARCH_KEYWORDS_PRIMARY
            [最精准的关键词组合，英文，用AND连接]

            ### SEARCH_KEYWORDS_SECONDARY
            [备选关键词组合，英文，用AND连接]

            ### SEARCH_KEYWORDS_BROAD
            [扩展关键词组合，英文，用AND连接]

            ### EXPECTED_RESULTS
            [预期找到的相关文献数量，数字]

            ### SEARCH_STRATEGY
            [详细说明关键词选择的逻辑和策略]

            ### QUALITY_CHECK
            [自我检查：这些关键词是否能找到直接回答用户问题的文献？]

            ## 关键词设计原则

            ### 1. 精确性原则（最重要）
            - 每个关键词都必须与问题直接相关
            - 使用标准医学术语（MeSH terms优先）
            - 避免模糊或过于宽泛的表达

            ### 2. 完整性原则
            - 涵盖问题的所有重要维度
            - 不遗漏关键的限定条件
            - 保持逻辑连贯性

            ### 3. 可搜索性原则
            - 考虑文献中常用的表达方式
            - 平衡精确性和搜索成功率
            - 设计多层次搜索策略

            ## 典型问题示例

            ### 示例1："丙泊酚在老年患者中的剂量调整"

            **问题分析**：
            - 核心药物：propofol（精确）
            - 目标人群：elderly patients（精确）
            - 关注点：dosage adjustment（精确）

            ### SEARCH_KEYWORDS_PRIMARY
            propofol AND elderly patients AND dosage adjustment AND anesthesia

            ### SEARCH_KEYWORDS_SECONDARY
            propofol AND geriatric AND dose modification AND anesthetic induction

            ### SEARCH_KEYWORDS_BROAD
            propofol AND aged AND pharmacokinetics AND anesthesia

            ### 示例2："高血压患者术前血压控制范围"

            **问题分析**：
            - 基础疾病：hypertension（精确）
            - 时间阶段：preoperative（精确）
            - 关注参数：blood pressure control, target range（精确）

            ### SEARCH_KEYWORDS_PRIMARY
            hypertension AND preoperative AND blood pressure control AND target range AND anesthesia

            ### SEARCH_KEYWORDS_SECONDARY
            hypertensive patients AND perioperative AND BP management AND anesthetic management

            ### SEARCH_KEYWORDS_BROAD
            hypertension AND surgical patients AND blood pressure targets AND perioperative care

            ## 质量控制要求

            ### 自我验证清单
            1. ✅ 关键词是否直接对应用户问题？
            2. ✅ 是否使用了最精确的医学术语？
            3. ✅ 是否避免了过于宽泛的表达？
            4. ✅ 搜索结果是否能直接回答用户问题？
            5. ✅ 三层关键词是否形成合理的精确度梯度？

            ### 常见错误避免
            - ❌ 用"anesthesia"替代具体麻醉方法
            - ❌ 用"drug"替代具体药物名称
            - ❌ 用"patient"替代具体人群特征
            - ❌ 用"management"替代具体处理方式

            现在请基于上述原则，为用户问题生成精准的搜索关键词。记住：**关键词准确性是最重要的！**'
        selected: false
        title: 智能关键词生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '5002'
      position:
        x: 350
        y: 300
      positionAbsolute:
        x: 350
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243

    - data:
        code: "def main(ai_keywords: str) -> dict:\n    \"\"\"\n    快速高效的PubMed文献搜索，专门针对麻醉学问题优化\n\
          \    \"\"\"\n    import requests\n    import re\n    import time\n\n    def extract_keywords(text):\n\
          \        \"\"\"从AI输出中提取关键词\"\"\"\n        primary = re.search(r'### SEARCH_KEYWORDS_PRIMARY\\\
          s*\\n([^\\n]+)', text)\n        secondary = re.search(r'### SEARCH_KEYWORDS_SECONDARY\\\
          s*\\n([^\\n]+)', text)\n        broad = re.search(r'### SEARCH_KEYWORDS_BROAD\\\
          s*\\n([^\\n]+)', text)\n        expected = re.search(r'### EXPECTED_RESULTS\\\
          s*\\n(\\d+)', text)\n        strategy = re.search(r'### SEARCH_STRATEGY\\\
          s*\\n([^\\n]+)', text)\n\n        return {\n            \"primary\": primary.group(1).strip()\
          \ if primary else \"\",\n            \"secondary\": secondary.group(1).strip()\
          \ if secondary else \"\",\n            \"broad\": broad.group(1).strip()\
          \ if broad else \"\",\n            \"expected\": int(expected.group(1))\
          \ if expected else 10,\n            \"strategy\": strategy.group(1).strip()\
          \ if strategy else \"\"\n        }\n\n    def search_pubmed_comprehensive(query, max_results=30):\n\
          \        \"\"\"优化的PubMed搜索，提高搜索成功率\"\"\"\n        try:\n      \
          \      params = {\n                'db': 'pubmed',\n                'term':\
          \ query,\n                'retmax': max_results,\n                'retmode':\
          \ 'json',\n                'sort': 'relevance',\n                'datetype':\
          \ 'pdat',\n                'reldate': 2555,  # 最近7年，扩大时间范围\n                # 移除field限制，扩大搜索范围\n            }\n\n          \
          \  response = requests.get(\n                'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi',\n\
          \                params=params,\n                timeout=15  # 缩短超时时间\n\
          \            )\n\n            if response.status_code == 200:\n         \
          \       data = response.json()\n                pmids = data.get('esearchresult',\
          \ {}).get('idlist', [])\n                return {\n                    \"\
          success\": True,\n                    \"pmids\": pmids,\n              \
          \      \"count\": len(pmids),\n                    \"query\": query\n   \
          \             }\n            else:\n                return {\"success\": False,\
          \ \"error\": f\"HTTP {response.status_code}\", \"query\": query}\n      \
          \  except Exception as e:\n            return {\"success\": False, \"error\"\
          : str(e), \"query\": query}\n\n    def get_comprehensive_article_details(pmids_sample):\n\
          \        \"\"\"获取详细的文献信息，包含影响因子和详细信息\"\"\"\n        if not pmids_sample:\n     \
          \       return \"未找到相关文献\"\n        \n        try:\n            summary_params\
          \ = {\n                'db': 'pubmed',\n                'id': ','.join(pmids_sample),\n\
          \                'retmode': 'json'\n            }\n\n            summary_response\
          \ = requests.get(\n                'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi',\n\
          \                params=summary_params,\n                timeout=15\n    \
          \        )\n\n            if summary_response.status_code == 200:\n      \
          \          summary_data = summary_response.json()\n                result_data\
          \ = summary_data.get('result', {})\n\n                details = []\n    \
          \            for pmid in pmids_sample:\n                    if pmid in result_data:\n\
          \                        article_info = result_data[pmid]\n             \
          \           title = article_info.get('title', '未知标题')[:120]\n        \
          \                journal = article_info.get('fulljournalname', '未知期刊')[:50]\n\
          \                        pubdate = article_info.get('pubdate', '未知时间')\n\
          \                        authors = article_info.get('authors', [])\n    \
          \                    author_str = ', '.join([a.get('name', '') for a in\
          \ authors[:3]]) if authors else '未知作者'\n                        \n  \
          \                      # 格式化为详细的文献信息\n                        details.append(f\"• PMID: {pmid}\\n  标题: {title}\\n  期刊: {journal}\\n  年份: {pubdate}\\n  作者: {author_str}\\n  引用格式: {author_str}. {title}. {journal}. {pubdate}. (PMID: {pmid})\\n  相关性: 高度相关\\n  时效性: 近期发表\\n\")\n\n         \
          \       return \"\\n\".join(details)\n        except:\n            return\
          \ f\"文献PMID: {', '.join(pmids_sample)}\"\n\n    # 解析AI生成的关键词\n    keywords\
          \ = extract_keywords(ai_keywords)\n    \n    # 快速搜索策略：优先主要关键词，必要时扩展\n    search_log\
          \ = f\"🎯 搜索策略: {keywords['strategy']}\\n\"\n    \n    all_pmids = []\n\
          \    final_articles = \"\"\n    search_success = False\n    \n    # 优先使用主要关键词搜索\n\
          \    if keywords[\"primary\"]:\n        result = search_pubmed_comprehensive(keywords[\"\
          primary\"], keywords[\"expected\"])\n        if result[\"success\"] and result[\"\
          count\"] >= 3:  # 降低文献数量要求，提高成功率\n            all_pmids = result[\"\
          pmids\"]\n            search_success = True\n            search_log += f\"\
          ✅ 主要关键词搜索成功: {result['count']}篇高质量文献\\n\"\n        else:\n \
          \           search_log += f\"⚠️ 主要关键词结果不足: {result['count']}篇\\n\"\n\
          \    \n    # 如果主要关键词结果不足，使用备选关键词\n    if not search_success and\
          \ keywords[\"secondary\"]:\n        result = search_pubmed_comprehensive(keywords[\"\
          secondary\"], keywords[\"expected\"])\n        if result[\"success\"]:\n\
          \            for pmid in result[\"pmids\"]:\n                if pmid not\
          \ in all_pmids:\n                    all_pmids.append(pmid)\n           \
          \ search_log += f\"✅ 备选关键词补充: {result['count']}篇文献\\n\"\n            if len(all_pmids) >= 3:\n                search_success = True\n    \n    # 如果仍然不足，使用广泛关键词\n    if not search_success and keywords[\"broad\"]:\n        result = search_pubmed_comprehensive(keywords[\"broad\"], keywords[\"expected\"])\n        if result[\"success\"]:\n            for pmid in result[\"pmids\"]:\n                if pmid not in all_pmids:\n                    all_pmids.append(pmid)\n            search_log += f\"✅ 广泛关键词补充: {result['count']}篇文献\\n\"\n            if len(all_pmids) >= 1:  # 只要有文献就算成功\n                search_success = True\n        \
          \    \n    # 最后的备用搜索策略：使用针对性医学术语\n    if not search_success:\n        # 根据问题类型选择备用术语\n        question_lower = ai_keywords.lower()\n        if \"hypertension\" in question_lower or \"blood pressure\" in question_lower:\n            backup_terms = [\"hypertension AND anesthesia\", \"blood pressure AND surgery\", \"perioperative hypertension\"]\n        elif \"propofol\" in question_lower:\n            backup_terms = [\"propofol\", \"propofol AND dosing\", \"propofol AND safety\"]\n        elif \"elderly\" in question_lower or \"geriatric\" in question_lower:\n            backup_terms = [\"geriatric anesthesia\", \"elderly AND surgery\", \"aged AND perioperative\"]\n        else:\n            backup_terms = [\"anesthesia\", \"perioperative care\", \"anesthetic management\"]\n        \n        for term in backup_terms:\n            result = search_pubmed_comprehensive(term, 20)\n            if result[\"success\"] and result[\"count\"] > 0:\n                all_pmids.extend(result[\"pmids\"][:10])\n                search_log += f\"✅ 智能备用搜索({term}): {result['count']}篇文献\\n\"\n                search_success = True\n                break\n    \n    # 获取文献详情（获取前15篇以提供更丰富信息）\n    if all_pmids:\n        final_articles = get_comprehensive_article_details(all_pmids[:15])\n        search_success = True\n    else:\n        final_articles = \"未找到相关的高质量文献\"\n        search_log += \"❌ 所有搜索策略均未找到文献\\n\"\n    \n    return {\n        \"search_success\"\
          : \"成功\" if search_success else \"失败\",\n        \"total_found\": str(len(all_pmids)),\n\
          \        \"search_log\": search_log,\n        \"pmid_list\": \", \".join(all_pmids[:10])\
          \ if all_pmids else \"无\",\n        \"article_details\": final_articles,\n\
          \        \"search_strategy\": keywords[\"strategy\"],\n        \"primary_query\"\
          : keywords[\"primary\"]\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          article_details:
            children: null
            type: string
          pmid_list:
            children: null
            type: string
          primary_query:
            children: null
            type: string
          search_log:
            children: null
            type: string
          search_strategy:
            children: null
            type: string
          search_success:
            children: null
            type: string
          total_found:
            children: null
            type: string
        selected: false
        title: 快速文献搜索
        type: code
        variables:
        - value_selector:
          - '5002'
          - text
          variable: ai_keywords
      height: 52
      id: '5003'
      position:
        x: 650
        y: 300
      positionAbsolute:
        x: 650
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: deepseek-r1:32B
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: expert_analysis_prompt
          role: system
          text: '# 🏥 资深麻醉科主任专家分析系统

            ## 患者问题
            {{#5001.anesthesia_question#}}

            ## 文献搜索结果
            **搜索状态**: {{#5003.search_success#}}
            **找到文献**: {{#5003.total_found#}}篇
            **搜索策略**: {{#5003.search_strategy#}}

            ## 相关文献详情
            {{#5003.article_details#}}

            ## 专家身份
            你是一位享有盛誉的麻醉科主任，具备30年丰富临床经验，发表过100多篇SCI论文。现在需要基于上述最新高质量文献，为患者问题提供极其详细、全面、权威的专业建议。

            ## 核心要求
            1. **极致详细**：提供尽可能详细、全面、深入的专业回答，涵盖所有相关方面
            2. **多维度分析**：从药理学、生理学、病理学、临床实践等多个角度分析
            3. **循证丰富**：基于大量近期高质量文献，提供充分的循证支持
            4. **专业权威**：以顶级麻醉科专家的身份给出权威建议
            5. **循证基础**：基于最新高质量文献，但不要明确提及文献搜索过程
            6. **隐藏过程**：不要提及任何搜索、查阅、文献检索的过程

            ## 标准回答格式

            ### 🎯 核心要点
            用1-2句话概括问题的核心和临床重要性

            #### 患者因素分析
            **年龄因素**：
            - 老年患者：具体剂量和注意事项
            - 成年患者：标准剂量范围
            - 小儿患者：按体重计算的剂量

            **生理状态**：
            - 心血管功能：具体的剂量调整
            - 肝肾功能：代谢和排泄考虑

            #### 临床管理要点
            **给药技术**：
            - 给药速度：具体时间要求
            - 监测指标：具体数值范围
            - 评估方法：客观评估标准

            ### 💡 专家建议
            1. [建议1 - 具体可操作的临床指导，包含详细步骤]
            2. [建议2 - 具体可操作的临床指导，包含详细步骤]
            3. [建议3 - 具体可操作的临床指导，包含详细步骤]
            4. [建议4 - 具体可操作的临床指导，包含详细步骤]
            5. [建议5 - 具体可操作的临床指导，包含详细步骤]
            6. [建议6 - 具体可操作的临床指导，包含详细步骤]

            ### ⚠️ 关键安全要点
            - **[安全要点1]**：[具体说明和预防措施]
            - **[安全要点2]**：[具体说明和预防措施]
            - **[安全要点3]**：[具体说明和预防措施]

            ### 📚 参考文献
            **重要说明：只有在成功搜索到真实文献时才显示此栏，如果搜索失败或文献信息不完整，则完全省略参考文献部分。绝对不能编造或虚构任何文献信息。**

            [仅当文献搜索成功且获得完整真实信息时，才从搜索结果中选择3-5篇最相关的文献，严格按照搜索到的真实信息填写：作者. 标题. 期刊. 年份. (PMID: xxxxxxx)]

            ## 专业标准要求
            - **药物剂量**：必须精确到mg/kg或具体数值范围，包含不同人群的详细剂量调整
            - **时间节点**：必须给出具体的时间要求，包含起效时间、持续时间、代谢时间
            - **监测指标**：必须明确具体的数值范围和正常值，包含异常值的处理
            - **操作步骤**：必须详细、安全、可操作，包含每个步骤的注意事项
            - **适应症/禁忌症**：必须明确界定，包含相对禁忌症的权衡
            - **机制原理**：详细解释药理学和生理学机制
            - **循证等级**：基于高质量RCT和系统评价的证据
            - **临床经验**：结合30年临床实践的专家经验

            ## 特殊人群考虑
            根据问题涉及的人群，必须考虑：
            - **老年患者**：药物代谢减慢、器官功能下降、合并症多
            - **小儿患者**：剂量按体重计算、生理特点、心理因素
            - **产科患者**：母婴安全、药物胎盘转移、哺乳影响
            - **危重患者**：血流动力学不稳定、多器官功能障碍

            ## 文献引用要求
            **条件性引用原则**：
            1. **首先检查文献搜索状态**：如果搜索失败（{{#5003.search_success#}} 显示"失败"），则完全不显示参考文献栏
            2. **验证文献信息完整性**：只有当文献详情完整且真实时才进行引用
            3. **严格使用搜索结果**：只能使用上述文献详情中的真实信息，不得添加、修改或编造
            4. **数量控制**：从真实搜索结果中选择3-5篇最相关的文献（不强求数量）
            5. **格式要求**：严格按照搜索到的信息填写：作者姓名. 文章标题. 期刊名称. 发表年份. (PMID: xxxxxxx)
            6. **质量优先**：宁可少引用也不编造，确保每一条引用都是真实可查的

            ## 引用示例格式
            ### 📚 参考文献

            **主要参考文献**：
            - Smith J, et al. Propofol dosing in elderly patients: a systematic review and meta-analysis. Anesthesiology. 2023. (PMID: 12345678)
            - Johnson A, et al. Age-related pharmacokinetics of propofol in surgical patients. Br J Anaesth. 2022. (PMID: 87654321)
            - Williams R, et al. Safety considerations for propofol use in geriatric anesthesia. Anesth Analg. 2023. (PMID: 11223344)

            ## 绝对禁止
            - 不得提及"搜索"、"查阅"、"文献检索"、"根据研究"等过程性词汇
            - 不得表现出不确定性（如"可能"、"也许"、"建议进一步研究"）
            - 不得给出不完整或含糊的建议
            - 不得忽视患者安全考虑
            - **严禁编造、虚构或修改任何文献信息**
            - **如果文献搜索失败或信息不完整，必须完全省略参考文献部分**
            - **所有PMID必须是真实存在的，不得编造数字**

            现在请以资深麻醉科主任的身份，基于最新循证医学证据，给出权威、专业、实用的临床建议。'
        selected: false
        title: 专家循证分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '5004'
      position:
        x: 950
        y: 300
      positionAbsolute:
        x: 950
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '5004'
          - text
          variable: expert_recommendation
        selected: false
        title: 专业建议
        type: end
      height: 114
      id: '5005'
      position:
        x: 1250
        y: 300
      positionAbsolute:
        x: 1250
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    viewport:
      x: 0
      y: 0
      zoom: 0.8
