# AI自主迭代优化智能体制作教程

## 📋 目录
1. [项目概述](#项目概述)
2. [环境准备](#环境准备)
3. [平台配置](#平台配置)
4. [工作流设计](#工作流设计)
5. [节点配置详解](#节点配置详解)
6. [代码实现](#代码实现)
7. [测试与调试](#测试与调试)
8. [部署上线](#部署上线)

## 🎯 项目概述

### 智能体功能特色
- **AI自主关键词生成**：智能分析医学问题并生成最优搜索策略
- **自我结果评估**：客观评估搜索质量，识别不足之处
- **自主策略优化**：不满意时自动改进搜索策略
- **迭代式精进**：持续优化直到AI满意为止

### 技术架构
- **平台**：Dify工作流平台
- **模型**：DeepSeek-R1:32B (通过Ollama集成)
- **数据源**：PubMed医学文献数据库
- **编程语言**：Python 3
- **API**：NCBI E-utilities API

## 🛠️ 环境准备

### 1. 基础环境要求
```bash
# 系统要求
- 操作系统：Windows/Linux/macOS
- Python版本：3.8+
- 内存：建议8GB以上
- 网络：稳定的互联网连接
```

### 2. 安装Dify平台
```bash
# 使用Docker安装Dify
git clone https://github.com/langgenius/dify.git
cd dify/docker
docker-compose up -d
```

### 3. 配置Ollama
```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载DeepSeek-R1模型
ollama pull deepseek-r1:32b
```

### 4. Python依赖包
```bash
pip install requests
pip install json
pip install re
```

## ⚙️ 平台配置

### 1. 创建新应用
1. 登录Dify管理界面
2. 点击"创建应用"
3. 选择"工作流"类型
4. 设置应用名称：`AI自主迭代优化完整版`
5. 设置图标：🧠
6. 设置背景色：#E3F2FD

### 2. 配置模型提供商
1. 进入"设置" → "模型提供商"
2. 添加Ollama提供商
3. 配置连接信息：
   - 基础URL：http://localhost:11434
   - 模型：deepseek-r1:32B

### 3. 安装插件依赖
在应用设置中添加依赖：
```yaml
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/ollama:0.0.6@f430f3eb959f4863b1e87171544a8fec179441b90deda5693c85f07712d2a68c
```

## 🔄 工作流设计

### 工作流程图
```
开始 → AI关键词生成 → 智能搜索执行 → AI结果评估 → 满意度判断
                                                    ↓
                                               [满意] → 最终分析_成功 → 结束
                                                    ↓
                                               [不满意] → AI优化策略 → 优化搜索执行 → 最终分析_优化 → 结束
```

### 节点类型说明
- **开始节点**：接收用户输入的医学问题
- **LLM节点**：AI推理和分析
- **代码节点**：执行PubMed搜索
- **条件节点**：判断搜索结果满意度
- **结束节点**：输出最终分析结果

## 📝 节点配置详解

### 1. 开始节点配置
```yaml
variables:
- label: 医学问题
  max_length: 300
  required: true
  type: text-input
  variable: medical_question
```

### 2. AI关键词生成节点
- **节点类型**：LLM
- **模型**：deepseek-r1:32B
- **温度**：0.3
- **提示词模板**：
```
# 🧠 AI医学文献搜索关键词生成专家

## 用户问题
{{#3001.medical_question#}}

## 任务
作为AI搜索专家，请分析用户问题并生成最优的PubMed搜索关键词。

## 分析要求
1. **问题类型识别**：确定这是什么类型的医学问题
2. **核心概念提取**：识别关键的医学概念
3. **搜索策略制定**：设计多层次搜索策略

## 输出格式（严格按此格式）

### SEARCH_KEYWORDS_PRIMARY
[主要搜索关键词，英文，用AND连接]

### SEARCH_KEYWORDS_SECONDARY
[备选搜索关键词，英文，用AND连接]

### SEARCH_KEYWORDS_BROAD
[扩展搜索关键词，英文，用AND连接]

### EXPECTED_RESULTS
[预期找到多少篇相关文献，只写数字]

### SEARCH_STRATEGY
[简要说明搜索策略和预期]

请严格按照上述格式输出，不要添加其他内容。
```

### 3. 智能搜索执行节点
- **节点类型**：代码
- **编程语言**：Python 3
- **输入变量**：ai_keywords (来自AI关键词生成节点)
- **输出变量**：
  - search_success: 搜索状态
  - total_found: 找到的文献数量
  - search_process: 搜索过程日志
  - pmid_list: PMID列表
  - article_sample: 文献样本
  - ai_strategy: AI策略
  - expected_count: 预期数量
  - primary_query: 主要查询

### 4. AI结果评估节点
- **节点类型**：LLM
- **模型**：deepseek-r1:32B
- **温度**：0.2
- **功能**：评估搜索结果质量，决定是否需要优化

### 5. 满意度判断节点
- **节点类型**：条件判断
- **判断条件**：评估结果是否包含"满意"
- **分支**：
  - True：进入最终分析_成功
  - False：进入AI优化策略

## 💻 代码实现

### 核心搜索代码详解

#### 1. 关键词提取函数
```python
def extract_keywords(text):
    """从AI输出中提取关键词"""
    primary = re.search(r'### SEARCH_KEYWORDS_PRIMARY\s*\n([^\n]+)', text)
    secondary = re.search(r'### SEARCH_KEYWORDS_SECONDARY\s*\n([^\n]+)', text)
    broad = re.search(r'### SEARCH_KEYWORDS_BROAD\s*\n([^\n]+)', text)
    expected = re.search(r'### EXPECTED_RESULTS\s*\n(\d+)', text)
    strategy = re.search(r'### SEARCH_STRATEGY\s*\n([^\n]+)', text)

    return {
        "primary": primary.group(1).strip() if primary else "",
        "secondary": secondary.group(1).strip() if secondary else "",
        "broad": broad.group(1).strip() if broad else "",
        "expected": int(expected.group(1)) if expected else 5,
        "strategy": strategy.group(1).strip() if strategy else ""
    }
```

#### 2. PubMed搜索函数
```python
def search_pubmed(query, max_results=10):
    """执行PubMed搜索"""
    try:
        params = {
            'db': 'pubmed',
            'term': query,
            'retmax': max_results,
            'retmode': 'json',
            'sort': 'relevance',
            'datetype': 'pdat',
            'reldate': 1095  # 最近3年
        }

        response = requests.get(
            'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi',
            params=params,
            timeout=30
        )

        if response.status_code == 200:
            data = response.json()
            pmids = data.get('esearchresult', {}).get('idlist', [])
            return {
                "success": True,
                "pmids": pmids,
                "count": len(pmids),
                "query": query
            }
        else:
            return {"success": False, "error": f"HTTP {response.status_code}", "query": query}
    except Exception as e:
        return {"success": False, "error": str(e), "query": query}
```

#### 3. 文献详情获取
```python
def get_article_details(pmids_sample):
    """获取文献详细信息"""
    try:
        summary_params = {
            'db': 'pubmed',
            'id': ','.join(pmids_sample),
            'retmode': 'json'
        }

        summary_response = requests.get(
            'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi',
            params=summary_params,
            timeout=30
        )

        if summary_response.status_code == 200:
            summary_data = summary_response.json()
            result_data = summary_data.get('result', {})

            details = []
            for pmid in pmids_sample:
                if pmid in result_data:
                    article_info = result_data[pmid]
                    title = article_info.get('title', '未知标题')[:100]
                    journal = article_info.get('fulljournalname', '未知期刊')
                    pubdate = article_info.get('pubdate', '未知时间')

                    details.append(f"PMID:{pmid} | {title}... | {journal} | {pubdate}")

            return "\n".join(details)
    except:
        return f"PMID列表: {', '.join(pmids_sample)}"
```

#### 4. 主函数完整实现
```python
def main(ai_keywords: str) -> dict:
    """
    根据AI生成的关键词执行智能PubMed搜索
    """
    import requests
    import re

    # 解析AI生成的关键词
    keywords = extract_keywords(ai_keywords)

    search_log = f"🎯 AI搜索策略: {keywords['strategy']}\n"
    search_log += f"📊 预期结果: {keywords['expected']}篇文献\n\n"

    all_pmids = []
    search_attempts = []

    # 按优先级执行搜索
    queries = [
        ("主要关键词", keywords["primary"]),
        ("备选关键词", keywords["secondary"]),
        ("扩展关键词", keywords["broad"])
    ]

    for query_type, query in queries:
        if not query:
            continue

        result = search_pubmed(query, keywords["expected"])
        search_attempts.append(f"{query_type}: '{query}' → {result['count']}篇" if result["success"] else f"{query_type}: '{query}' → 失败")

        if result["success"]:
            # 去重添加PMID
            for pmid in result["pmids"]:
                if pmid not in all_pmids:
                    all_pmids.append(pmid)

            # 如果主要关键词结果充足，就不用继续
            if query_type == "主要关键词" and result["count"] >= keywords["expected"]:
                search_log += f"✅ {query_type}搜索成功，结果充足\n"
                break

        search_log += f"{'✅' if result['success'] else '❌'} {query_type}: {result['count']}篇\n"

    # 获取文献详情样本
    article_sample = ""
    if all_pmids:
        article_sample = get_article_details(all_pmids[:5])

    return {
        "search_success": "成功" if all_pmids else "失败",
        "total_found": str(len(all_pmids)),
        "search_process": search_log + "\n".join(search_attempts),
        "pmid_list": ", ".join(all_pmids[:10]) if all_pmids else "无",
        "article_sample": article_sample if article_sample else "未获取到文献详情",
        "ai_strategy": keywords["strategy"],
        "expected_count": str(keywords["expected"]),
        "primary_query": keywords["primary"]
    }
```

## 🧪 测试与调试

### 1. 单元测试
- 测试关键词提取功能
- 测试PubMed API调用
- 测试结果解析功能

### 2. 集成测试
- 测试完整工作流
- 测试各种医学问题类型
- 测试异常情况处理

### 3. 性能测试
- 测试响应时间
- 测试并发处理能力
- 测试资源使用情况

## 🚀 部署上线

### 1. 生产环境配置
- 配置生产数据库
- 设置环境变量
- 配置日志系统

### 2. 监控告警
- 设置性能监控
- 配置错误告警
- 建立日志分析

### 3. 用户权限
- 设置访问控制
- 配置用户角色
- 建立审计日志

## 📚 扩展功能

### 可扩展方向
1. **多语言支持**：支持中文医学术语搜索
2. **更多数据源**：集成其他医学数据库
3. **结果可视化**：添加图表和统计分析
4. **用户个性化**：记住用户偏好和历史
5. **批量处理**：支持批量问题处理

### 优化建议
1. **缓存机制**：缓存常见搜索结果
2. **负载均衡**：分布式部署提高性能
3. **智能推荐**：基于历史数据推荐相关问题
4. **质量评估**：建立文献质量评分系统

## ❓ 常见问题

### Q1: 如何处理API限制？
A: 实现请求频率控制和重试机制

### Q2: 如何提高搜索准确性？
A: 优化关键词生成算法，增加同义词库

### Q3: 如何处理网络异常？
A: 实现超时重试和降级策略

---

**制作完成时间**：约2-4小时  
**难度等级**：中级  
**维护成本**：低
