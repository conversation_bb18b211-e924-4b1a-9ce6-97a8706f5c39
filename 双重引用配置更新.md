# 🔗 双重引用格式配置更新

## 📋 更新概述

根据您的要求，我已经更新了麻醉智能体配置，现在回答中将同时包含：
1. **【麻海新知】综述引用** - 提供权威性
2. **具体论文PMID引用** - 提供可追溯性

## 🔄 主要更新内容

### 1. 提示词模板更新
```yaml
新增引用要求:
  - 必须使用搜索到的真实文献
  - 从文献详情中选择2-3篇最相关的
  - 包含完整的PMID号码
  - 标准化的引用格式
```

### 2. 文献信息格式优化
```python
# 更新后的文献信息格式
details.append(f"""
• PMID: {pmid}
  标题: {title}
  期刊: {journal}
  年份: {pubdate}
  作者: {author_str}
  引用格式: {author_str}. {title}. {journal}. {pubdate}. (PMID: {pmid})
""")
```

### 3. 回答格式标准化
```markdown
### 📚 循证依据
*引用自【麻海新知】[相关综述标题]*

**主要参考文献**：
- 作者. 标题. 期刊. 年份. (PMID: xxxxxxx)
- 作者. 标题. 期刊. 年份. (PMID: xxxxxxx)
- 作者. 标题. 期刊. 年份. (PMID: xxxxxxx)
```

## ✅ 更新优势

### 1. 增强可信度
- **权威背景**：【麻海新知】提供专业权威性
- **循证基础**：PMID提供可追溯的科学依据
- **双重保障**：综述+原始文献的双重支撑

### 2. 提升实用性
- **便于查证**：读者可通过PMID查找原文
- **专业认可**：符合医学界引用习惯
- **质量保证**：基于真实最新文献

### 3. 保持专业性
- **格式统一**：标准化的引用格式
- **内容权威**：麻醉科主任级别的专业建议
- **信息完整**：包含作者、期刊、年份等完整信息

## 🎯 使用效果

### 示例回答格式
```markdown
### 🎯 核心要点
[专业的麻醉学建议内容]

#### [分类内容]
**[子分类]**：
- [具体建议和数值]
- [操作要点]

### 💡 专家建议
1. [具体可操作的建议]
2. [临床指导要点]

### ⚠️ 关键安全要点
- **[安全要点]**：[具体说明]

### 📚 循证依据
*引用自【麻海新知】丙泊酚在老年患者中的药代动力学特征与个体化给药策略*

**主要参考文献**：
- Smith J, et al. Propofol dosing in elderly patients. Anesthesiology. 2023. (PMID: 12345678)
- Johnson A, et al. Age-related pharmacokinetics of propofol. Br J Anaesth. 2022. (PMID: 87654321)
- Williams R, et al. Safety considerations for propofol in geriatrics. Anesth Analg. 2023. (PMID: 11223344)
```

## 📊 质量控制

### 文献选择标准
```yaml
优先级排序:
  1. 相关性: 与问题高度相关
  2. 时效性: 优先最近5年发表
  3. 权威性: 高影响因子期刊
  4. 质量: 大样本、高质量研究
```

### 引用数量控制
```yaml
引用规范:
  - 【麻海新知】: 1个相关综述标题
  - 具体文献: 2-3篇最相关论文
  - PMID格式: 完整准确的PMID号
  - 信息完整: 作者、标题、期刊、年份
```

## 🔧 技术实现

### 1. 搜索优化
- 智能关键词生成
- 相关性排序
- 时效性筛选
- 质量评估

### 2. 信息提取
- 文献标题提取
- 作者信息整理
- 期刊名称获取
- 发表年份确认
- PMID号码验证

### 3. 格式生成
- 标准引用格式
- 双重引用结构
- 信息完整性检查
- 格式一致性保证

## 🧪 测试验证

### 测试要点
```yaml
验证项目:
  - 文献搜索成功率: >95%
  - 引用格式正确性: 100%
  - PMID有效性: 100%
  - 内容相关性: >90%
  - 专业水平: 麻醉科主任级
```

### 测试问题
```
1. 丙泊酚在老年患者中的剂量调整原则？
2. 过敏性休克的紧急处理流程？
3. 困难气道的术前评估要点？
4. 椎管内麻醉的并发症预防？
5. 小儿麻醉的特殊注意事项？
```

## 📈 预期效果

### 用户体验
- **专业可信**：双重引用增强可信度
- **便于查证**：PMID便于查找原文
- **格式规范**：符合医学文献习惯
- **内容权威**：基于最新循证证据

### 专业认可
- **麻醉科主任级别**：专业水平达标
- **循证医学基础**：基于真实文献
- **临床实用性**：可直接应用指导
- **安全性保障**：充分强调患者安全

## 🚀 部署说明

### 1. 配置文件
使用更新后的 `麻醉智能体_完整版.yml`

### 2. 环境要求
- Dify平台正常运行
- DeepSeek-R1:32B模型可用
- PubMed API网络连接正常
- 足够的计算资源支持

### 3. 验证步骤
1. 导入更新后的配置文件
2. 测试文献搜索功能
3. 验证双重引用格式
4. 检查回答专业水平
5. 确认PMID有效性

## ⚠️ 注意事项

### 1. 网络依赖
- 需要稳定的PubMed API访问
- 建议配置网络重试机制
- 监控API调用成功率

### 2. 质量监控
- 定期检查引用文献质量
- 验证PMID有效性
- 收集用户反馈
- 持续优化引用策略

### 3. 格式一致性
- 保持引用格式统一
- 确保信息完整准确
- 维护专业标准
- 定期更新引用模板

---

**更新特点**: 双重引用格式 = 【麻海新知】+ 具体PMID  
**核心优势**: 权威性 + 可追溯性 + 专业性  
**适用场景**: 需要高质量循证医学建议的专业咨询
