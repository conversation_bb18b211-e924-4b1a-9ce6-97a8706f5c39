# 📁 项目结构说明

## 🗂️ 文件组织

```
AI自主迭代优化智能体/
├── 📄 README.md                                    # 项目总览和快速开始
├── 📄 麻醉智能体.yml                                # Dify智能体配置文件
├── 📚 AI自主迭代优化智能体制作教程.md                  # 详细制作教程
├── 📖 AI自主迭代优化智能体使用手册.md                  # 用户使用指南
├── 🔧 AI自主迭代优化智能体技术文档.md                  # 技术实现文档
├── 📋 项目结构说明.md                               # 本文件
├── 📄 LICENSE                                      # 开源许可证
├── 📄 requirements.txt                             # Python依赖包
├── 📄 requirements-dev.txt                         # 开发环境依赖
├── 📄 .env.example                                 # 环境变量示例
├── 📄 .gitignore                                   # Git忽略文件
├── 📄 docker-compose.yml                           # Docker编排文件
├── 📄 Dockerfile                                   # Docker镜像构建
└── 📁 docs/                                        # 额外文档目录
    ├── 📄 API文档.md                               # API接口文档
    ├── 📄 部署指南.md                               # 生产环境部署
    ├── 📄 故障排除.md                               # 常见问题解决
    └── 📁 images/                                  # 文档图片资源
        ├── 🖼️ architecture.png                     # 架构图
        ├── 🖼️ workflow.png                         # 工作流程图
        └── 🖼️ ui-screenshots/                      # 界面截图
```

## 📄 核心文件说明

### 1. 配置文件

#### `麻醉智能体.yml`
- **用途**: Dify平台的智能体配置文件
- **内容**: 完整的工作流定义、节点配置、提示词模板
- **格式**: YAML格式，可直接导入Dify平台
- **重要性**: ⭐⭐⭐⭐⭐ (核心配置文件)

```yaml
# 主要配置项
app:
  name: AI自主迭代优化完整版
  mode: workflow
  icon: 🧠

workflow:
  nodes: [开始, AI关键词生成, 智能搜索执行, AI结果评估, ...]
  edges: [节点连接关系]
```

#### `requirements.txt`
- **用途**: Python项目依赖包列表
- **内容**: 运行时必需的Python包
- **使用**: `pip install -r requirements.txt`

```txt
requests>=2.28.0
aiohttp>=3.8.0
redis>=4.3.0
prometheus-client>=0.14.0
```

#### `.env.example`
- **用途**: 环境变量配置示例
- **内容**: 所有可配置的环境变量及默认值
- **使用**: 复制为`.env`文件并修改实际值

```bash
# AI模型配置
OLLAMA_URL=http://localhost:11434
MODEL_NAME=deepseek-r1:32b

# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost/db
REDIS_URL=redis://localhost:6379
```

### 2. 文档文件

#### `README.md`
- **用途**: 项目总体介绍和快速开始指南
- **受众**: 所有用户（开发者、使用者、贡献者）
- **内容**: 
  - 项目概述和特色功能
  - 快速安装和使用步骤
  - 技术栈和架构概览
  - 贡献指南和联系方式

#### `AI自主迭代优化智能体制作教程.md`
- **用途**: 详细的制作和部署教程
- **受众**: 开发者、系统管理员
- **内容**:
  - 环境准备和依赖安装
  - 平台配置和工作流设计
  - 节点配置和代码实现
  - 测试调试和部署上线

#### `AI自主迭代优化智能体使用手册.md`
- **用途**: 终端用户操作指南
- **受众**: 医学研究人员、临床医生
- **内容**:
  - 功能介绍和使用技巧
  - 最佳实践和注意事项
  - 常见问题和解决方案
  - 结果解读和应用建议

#### `AI自主迭代优化智能体技术文档.md`
- **用途**: 深度技术实现文档
- **受众**: 高级开发者、架构师
- **内容**:
  - 系统架构和技术栈
  - 核心算法和数据流程
  - API接口和性能优化
  - 安全机制和监控告警

### 3. 部署文件

#### `docker-compose.yml`
- **用途**: Docker容器编排配置
- **内容**: 多服务容器的定义和依赖关系
- **使用**: `docker-compose up -d`

```yaml
version: '3.8'
services:
  ai-search:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - redis
      - postgres
  
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
```

#### `Dockerfile`
- **用途**: Docker镜像构建配置
- **内容**: 应用容器化的完整步骤
- **使用**: `docker build -t ai-medical-search .`

## 📚 文档阅读顺序

### 🚀 快速上手用户
1. `README.md` - 了解项目概况
2. `AI自主迭代优化智能体使用手册.md` - 学习如何使用
3. 开始使用智能体

### 🛠️ 开发部署人员
1. `README.md` - 项目概览
2. `AI自主迭代优化智能体制作教程.md` - 详细制作步骤
3. `AI自主迭代优化智能体技术文档.md` - 深入技术细节
4. `麻醉智能体.yml` - 配置文件分析

### 🔧 系统维护人员
1. `AI自主迭代优化智能体技术文档.md` - 系统架构理解
2. `docs/部署指南.md` - 生产环境部署
3. `docs/故障排除.md` - 问题诊断解决

### 📖 研究学习人员
1. `README.md` - 项目背景
2. `AI自主迭代优化智能体技术文档.md` - 算法原理
3. `麻醉智能体.yml` - 工作流设计
4. 所有文档 - 全面理解

## 🔄 文档维护

### 更新频率
- **README.md**: 每个版本发布时更新
- **使用手册**: 功能变更时更新
- **技术文档**: 架构调整时更新
- **制作教程**: 部署流程变化时更新

### 版本控制
- 所有文档都纳入Git版本控制
- 重要变更需要在更新日志中记录
- 保持文档与代码版本同步

### 质量保证
- 定期检查文档的准确性
- 收集用户反馈并改进
- 保持文档的可读性和实用性

## 📋 使用建议

### 1. 首次接触
- 从`README.md`开始，获得整体印象
- 根据角色选择对应的详细文档
- 按照文档步骤逐步操作

### 2. 深入学习
- 结合多个文档交叉阅读
- 实际操作验证文档内容
- 记录遇到的问题和解决方案

### 3. 贡献改进
- 发现文档错误及时反馈
- 提供使用经验和最佳实践
- 参与文档的完善和更新

## 🎯 文档特色

### 1. 结构化组织
- 按用户角色分类
- 按使用场景组织
- 清晰的目录和导航

### 2. 实用性导向
- 提供具体的操作步骤
- 包含实际的代码示例
- 解决真实的使用问题

### 3. 持续更新
- 跟随项目发展更新
- 收集用户反馈改进
- 保持内容的时效性

---

**文档维护**: AI医学团队  
**最后更新**: 2024年1月  
**版本**: v1.0.0
