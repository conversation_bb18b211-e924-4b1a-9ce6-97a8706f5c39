app:
  description: AI自主迭代优化搜索 - 完整版
  icon: 🧠
  icon_background: '#E3F2FD'
  mode: workflow
  name: AI自主迭代优化完整版
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/ollama:0.0.6@f430f3eb959f4863b1e87171544a8fec179441b90deda5693c85f07712d2a68c
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: '🧠 **AI自主迭代优化搜索系统**


      我是您的智能医学文献助手，具备完全自主的搜索优化能力：


      ✨ **AI自主关键词生成**：智能分析问题并生成最优搜索策略

      🔄 **自我结果评估**：客观评估搜索质量，识别不足

      🎯 **自主策略优化**：不满意时自动改进搜索策略

      🔁 **迭代式精进**：持续优化直到AI满意为止


      **核心特色**：

      - 真正的AI自主决策，无需人工干预

      - 多轮迭代优化，确保最佳搜索结果

      - 基于最新PubMed文献的循证分析

      - 完整的AI思考过程展示


      请输入您的医学问题，AI将自主完成整个搜索优化过程！

      '
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 3001-source-3002-target
      source: '3001'
      sourceHandle: source
      target: '3002'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: 3002-source-3003-target
      source: '3002'
      sourceHandle: source
      target: '3003'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: 3003-source-3004-target
      source: '3003'
      sourceHandle: source
      target: '3004'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: if-else
      id: 3004-source-3005-target
      source: '3004'
      sourceHandle: source
      target: '3005'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 3005-source-3006-target
      source: '3005'
      sourceHandle: 'true'
      target: '3006'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 3005-source-3007-target
      source: '3005'
      sourceHandle: 'false'
      target: '3007'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: 3007-source-3008-target
      source: '3007'
      sourceHandle: source
      target: '3008'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: 3008-source-3009-target
      source: '3008'
      sourceHandle: source
      target: '3009'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 3006-source-3010-target
      source: '3006'
      sourceHandle: source
      target: '3010'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 3009-source-3010-target
      source: '3009'
      sourceHandle: source
      target: '3010'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 医学问题
          max_length: 300
          options: []
          required: true
          type: text-input
          variable: medical_question
      height: 88
      id: '3001'
      position:
        x: 50
        y: 300
      positionAbsolute:
        x: 50
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.3
          mode: chat
          name: deepseek-r1:32B
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: keyword_prompt
          role: system
          text: '# 🧠 AI医学文献搜索关键词生成专家


            ## 用户问题

            {{#3001.medical_question#}}


            ## 任务

            作为AI搜索专家，请分析用户问题并生成最优的PubMed搜索关键词。


            ## 分析要求

            1. **问题类型识别**：确定这是什么类型的医学问题

            2. **核心概念提取**：识别关键的医学概念

            3. **搜索策略制定**：设计多层次搜索策略


            ## 输出格式（严格按此格式）


            ### SEARCH_KEYWORDS_PRIMARY

            [主要搜索关键词，英文，用AND连接]


            ### SEARCH_KEYWORDS_SECONDARY

            [备选搜索关键词，英文，用AND连接]


            ### SEARCH_KEYWORDS_BROAD

            [扩展搜索关键词，英文，用AND连接]


            ### EXPECTED_RESULTS

            [预期找到多少篇相关文献，只写数字]


            ### SEARCH_STRATEGY

            [简要说明搜索策略和预期]


            例如：

            ### SEARCH_KEYWORDS_PRIMARY

            propofol AND elderly AND anesthesia


            ### SEARCH_KEYWORDS_SECONDARY

            propofol AND geriatric AND safety


            ### SEARCH_KEYWORDS_BROAD

            propofol AND aged AND perioperative


            ### EXPECTED_RESULTS

            8


            ### SEARCH_STRATEGY

            针对丙泊酚在老年患者中的应用，采用从具体到一般的搜索策略


            请严格按照上述格式输出，不要添加其他内容。

            '
        selected: false
        title: AI关键词生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '3002'
      position:
        x: 350
        y: 300
      positionAbsolute:
        x: 350
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        code: "def main(ai_keywords: str) -> dict:\n    \"\"\"\n    根据AI生成的关键词执行智能PubMed搜索\n\
          \    \"\"\"\n    import requests\n    import re\n\n    def extract_keywords(text):\n\
          \        \"\"\"从AI输出中提取关键词\"\"\"\n        primary = re.search(r'### SEARCH_KEYWORDS_PRIMARY\\\
          s*\\n([^\\n]+)', text)\n        secondary = re.search(r'### SEARCH_KEYWORDS_SECONDARY\\\
          s*\\n([^\\n]+)', text)\n        broad = re.search(r'### SEARCH_KEYWORDS_BROAD\\\
          s*\\n([^\\n]+)', text)\n        expected = re.search(r'### EXPECTED_RESULTS\\\
          s*\\n(\\d+)', text)\n        strategy = re.search(r'### SEARCH_STRATEGY\\\
          s*\\n([^\\n]+)', text)\n\n        return {\n            \"primary\": primary.group(1).strip()\
          \ if primary else \"\",\n            \"secondary\": secondary.group(1).strip()\
          \ if secondary else \"\",\n            \"broad\": broad.group(1).strip()\
          \ if broad else \"\",\n            \"expected\": int(expected.group(1))\
          \ if expected else 5,\n            \"strategy\": strategy.group(1).strip()\
          \ if strategy else \"\"\n        }\n\n    def search_pubmed(query, max_results=10):\n\
          \        \"\"\"执行PubMed搜索\"\"\"\n        try:\n            params = {\n\
          \                'db': 'pubmed',\n                'term': query,\n     \
          \           'retmax': max_results,\n                'retmode': 'json',\n\
          \                'sort': 'relevance',\n                'datetype': 'pdat',\n\
          \                'reldate': 1095  # 最近3年\n            }\n\n            response\
          \ = requests.get(\n                'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi',\n\
          \                params=params,\n                timeout=30\n          \
          \  )\n\n            if response.status_code == 200:\n                data\
          \ = response.json()\n                pmids = data.get('esearchresult', {}).get('idlist',\
          \ [])\n                return {\n                    \"success\": True,\n\
          \                    \"pmids\": pmids,\n                    \"count\": len(pmids),\n\
          \                    \"query\": query\n                }\n            else:\n\
          \                return {\"success\": False, \"error\": f\"HTTP {response.status_code}\"\
          , \"query\": query}\n        except Exception as e:\n            return\
          \ {\"success\": False, \"error\": str(e), \"query\": query}\n\n    # 解析AI生成的关键词\n\
          \    keywords = extract_keywords(ai_keywords)\n\n    search_log = f\"\U0001F3AF\
          \ AI搜索策略: {keywords['strategy']}\\n\"\n    search_log += f\"\U0001F4CA 预期结果:\
          \ {keywords['expected']}篇文献\\n\\n\"\n\n    all_pmids = []\n    search_attempts\
          \ = []\n\n    # 按优先级执行搜索\n    queries = [\n        (\"主要关键词\", keywords[\"\
          primary\"]),\n        (\"备选关键词\", keywords[\"secondary\"]),\n        (\"\
          扩展关键词\", keywords[\"broad\"])\n    ]\n\n    for query_type, query in queries:\n\
          \        if not query:\n            continue\n\n        result = search_pubmed(query,\
          \ keywords[\"expected\"])\n        search_attempts.append(f\"{query_type}:\
          \ '{query}' → {result['count']}篇\" if result[\"success\"] else f\"{query_type}:\
          \ '{query}' → 失败\")\n\n        if result[\"success\"]:\n            # 去重添加PMID\n\
          \            for pmid in result[\"pmids\"]:\n                if pmid not\
          \ in all_pmids:\n                    all_pmids.append(pmid)\n\n        \
          \    # 如果主要关键词结果充足，就不用继续\n            if query_type == \"主要关键词\" and result[\"\
          count\"] >= keywords[\"expected\"]:\n                search_log += f\"✅\
          \ {query_type}搜索成功，结果充足\\n\"\n                break\n\n        search_log\
          \ += f\"{'✅' if result['success'] else '❌'} {query_type}: {result['count']}篇\\\
          n\"\n\n    # 获取文献详情样本\n    article_sample = \"\"\n    if all_pmids:\n  \
          \      try:\n            # 获取前5篇的详情\n            pmids_sample = all_pmids[:5]\n\
          \            summary_params = {\n                'db': 'pubmed',\n     \
          \           'id': ','.join(pmids_sample),\n                'retmode': 'json'\n\
          \            }\n\n            summary_response = requests.get(\n       \
          \         'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi',\n\
          \                params=summary_params,\n                timeout=30\n  \
          \          )\n\n            if summary_response.status_code == 200:\n  \
          \              summary_data = summary_response.json()\n                result_data\
          \ = summary_data.get('result', {})\n\n                details = []\n   \
          \             for pmid in pmids_sample:\n                    if pmid in\
          \ result_data:\n                        article_info = result_data[pmid]\n\
          \                        title = article_info.get('title', '未知标题')[:100]\n\
          \                        journal = article_info.get('fulljournalname', '未知期刊')\n\
          \                        pubdate = article_info.get('pubdate', '未知时间')\n\
          \n                        details.append(f\"PMID:{pmid} | {title}... | {journal}\
          \ | {pubdate}\")\n\n                article_sample = \"\\n\".join(details)\n\
          \        except:\n            article_sample = f\"PMID列表: {', '.join(all_pmids[:10])}\"\
          \n\n    return {\n        \"search_success\": \"成功\" if all_pmids else \"\
          失败\",\n        \"total_found\": str(len(all_pmids)),\n        \"search_process\"\
          : search_log + \"\\n\".join(search_attempts),\n        \"pmid_list\": \"\
          , \".join(all_pmids[:10]) if all_pmids else \"无\",\n        \"article_sample\"\
          : article_sample if article_sample else \"未获取到文献详情\",\n        \"ai_strategy\"\
          : keywords[\"strategy\"],\n        \"expected_count\": str(keywords[\"expected\"\
          ]),\n        \"primary_query\": keywords[\"primary\"]\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          ai_strategy:
            children: null
            type: string
          article_sample:
            children: null
            type: string
          expected_count:
            children: null
            type: string
          pmid_list:
            children: null
            type: string
          primary_query:
            children: null
            type: string
          search_process:
            children: null
            type: string
          search_success:
            children: null
            type: string
          total_found:
            children: null
            type: string
        selected: false
        title: 智能搜索执行
        type: code
        variables:
        - value_selector:
          - '3002'
          - text
          variable: ai_keywords
      height: 52
      id: '3003'
      position:
        x: 650
        y: 300
      positionAbsolute:
        x: 650
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.2
          mode: chat
          name: deepseek-r1:32B
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: evaluation_prompt
          role: system
          text: '# 🤖 AI搜索结果评估专家


            ## 原始问题

            {{#3001.medical_question#}}


            ## AI搜索策略

            {{#3003.ai_strategy#}}


            ## 搜索过程

            {{#3003.search_process#}}


            ## 搜索结果

            - 状态: {{#3003.search_success#}}

            - 找到文献: {{#3003.total_found#}}篇 (预期: {{#3003.expected_count#}}篇)

            - PMID: {{#3003.pmid_list#}}


            ## 文献样本

            {{#3003.article_sample#}}


            ## 评估任务

            作为AI评估专家，请客观评估本次搜索结果的质量：


            ### 评估标准

            1. **数量充足性**: 文献数量是否达到预期

            2. **相关性**: 文献是否与问题高度相关

            3. **质量**: 期刊和研究质量如何

            4. **时效性**: 文献是否足够新


            ### 输出格式（严格按此格式）


            ### SATISFACTION_LEVEL

            [满意/不满意]


            ### QUALITY_SCORE

            [1-10分，整数]


            ### MAIN_ISSUES

            [主要问题，如果满意则写"无"]


            ### OPTIMIZATION_SUGGESTIONS

            [具体的优化建议，如果满意则写"无需优化"]


            ### REASONING

            [评估理由，简要说明]


            请严格按照上述格式输出，基于实际搜索结果进行客观评估。

            '
        selected: false
        title: AI结果评估
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '3004'
      position:
        x: 950
        y: 300
      positionAbsolute:
        x: 950
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: satisfaction_check
            value: 满意
            variable_selector:
            - '3004'
            - text
          logical_operator: and
        conditions:
        - comparison_operator: contains
          id: satisfaction_check
          value: 满意
          variable_selector:
          - '3004'
          - text
        desc: ''
        logical_operator: and
        selected: false
        title: 满意度判断
        type: if-else
      height: 124
      id: '3005'
      position:
        x: 1250
        y: 300
      positionAbsolute:
        x: 1250
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.3
          mode: chat
          name: deepseek-r1:32B
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: final_analysis_prompt
          role: system
          text: '# 🎯 AI最终分析报告（首次成功）


            ## 用户问题

            {{#3001.medical_question#}}


            ## AI搜索历程

            **首次搜索即成功**

            - 搜索策略: {{#3003.ai_strategy#}}

            - 找到文献: {{#3003.total_found#}}篇

            - 文献样本: {{#3003.article_sample#}}

            - AI评估: {{#3004.text#}}


            ## AI自主分析任务

            基于AI自主搜索成功的过程，提供最终的专业分析：


            # 🧠 **AI自主搜索成功报告**


            ## 🔍 **AI搜索智能化展示**

            - **策略生成**: 展示AI如何分析问题并生成搜索策略

            - **执行效果**: 分析AI搜索策略的成功之处

            - **自我评估**: 展示AI的自我评估能力


            ## 📚 **文献质量分析**

            - **文献相关性**: 基于搜索结果评估文献与问题的匹配度

            - **研究价值**: 分析这些文献的学术和临床价值

            - **证据等级**: 评估文献的证据强度


            ## 💡 **循证医学建议**

            - **主要发现**: 基于文献的核心发现

            - **临床指导**: 对临床实践的具体建议

            - **安全要点**: 重要的安全提醒

            - **应用建议**: 如何在实践中应用这些证据


            ## 🚀 **AI系统优势**

            - **智能化程度**: 展示AI的自主决策能力

            - **效率优势**: 一次搜索即获得满意结果

            - **质量保证**: AI自我评估确保结果质量


            ## 📈 **持续改进建议**

            - **进一步研究方向**: 基于当前结果的研究建议

            - **知识更新**: 需要关注的最新进展


            请提供专业、全面、实用的分析报告。

            '
        selected: false
        title: 最终分析_成功
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '3006'
      position:
        x: 1550
        y: 200
      positionAbsolute:
        x: 1550
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.4
          mode: chat
          name: deepseek-r1:32B
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: optimization_prompt
          role: system
          text: '# 🔧 AI搜索优化策略生成器


            ## 原始问题

            {{#3001.medical_question#}}


            ## 上次搜索评估

            {{#3004.text#}}


            ## 上次搜索详情

            - 策略: {{#3003.ai_strategy#}}

            - 主要查询: {{#3003.primary_query#}}

            - 结果: {{#3003.total_found#}}篇


            ## 任务

            基于评估结果，生成改进的搜索策略。


            ## 优化原则

            1. **关键词优化**: 调整医学术语的精确性

            2. **搜索范围**: 扩大或缩小搜索范围

            3. **同义词**: 使用更多同义词和相关术语

            4. **过滤调整**: 修改时间范围或研究类型


            ## 输出格式（严格按此格式）


            ### SEARCH_KEYWORDS_PRIMARY

            [优化后的主要搜索关键词]


            ### SEARCH_KEYWORDS_SECONDARY

            [优化后的备选搜索关键词]


            ### SEARCH_KEYWORDS_BROAD

            [优化后的扩展搜索关键词]


            ### EXPECTED_RESULTS

            [调整后的预期结果数量]


            ### SEARCH_STRATEGY

            [优化策略说明]


            ### OPTIMIZATION_REASON

            [为什么这样优化，针对上次的不足]


            请基于上次搜索的不足，生成更好的搜索策略。

            '
        selected: false
        title: AI优化策略
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '3007'
      position:
        x: 1550
        y: 400
      positionAbsolute:
        x: 1550
        y: 400
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        code: "def main(optimization_keywords: str) -> dict:\n    \"\"\"\n    执行优化后的搜索策略\n\
          \    \"\"\"\n    import requests\n    import re\n\n    def extract_keywords(text):\n\
          \        \"\"\"从AI输出中提取关键词\"\"\"\n        primary = re.search(r'### SEARCH_KEYWORDS_PRIMARY\\\
          s*\\n([^\\n]+)', text)\n        secondary = re.search(r'### SEARCH_KEYWORDS_SECONDARY\\\
          s*\\n([^\\n]+)', text)\n        broad = re.search(r'### SEARCH_KEYWORDS_BROAD\\\
          s*\\n([^\\n]+)', text)\n        expected = re.search(r'### EXPECTED_RESULTS\\\
          s*\\n(\\d+)', text)\n        strategy = re.search(r'### SEARCH_STRATEGY\\\
          s*\\n([^\\n]+)', text)\n        reason = re.search(r'### OPTIMIZATION_REASON\\\
          s*\\n([^\\n]+)', text)\n\n        return {\n            \"primary\": primary.group(1).strip()\
          \ if primary else \"\",\n            \"secondary\": secondary.group(1).strip()\
          \ if secondary else \"\",\n            \"broad\": broad.group(1).strip()\
          \ if broad else \"\",\n            \"expected\": int(expected.group(1))\
          \ if expected else 8,\n            \"strategy\": strategy.group(1).strip()\
          \ if strategy else \"\",\n            \"reason\": reason.group(1).strip()\
          \ if reason else \"\"\n        }\n\n    def search_pubmed(query, max_results=12):\n\
          \        \"\"\"执行PubMed搜索\"\"\"\n        try:\n            params = {\n\
          \                'db': 'pubmed',\n                'term': query,\n     \
          \           'retmax': max_results,\n                'retmode': 'json',\n\
          \                'sort': 'relevance',\n                'datetype': 'pdat',\n\
          \                'reldate': 1825  # 扩大到5年\n            }\n\n           \
          \ response = requests.get(\n                'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi',\n\
          \                params=params,\n                timeout=30\n          \
          \  )\n\n            if response.status_code == 200:\n                data\
          \ = response.json()\n                pmids = data.get('esearchresult', {}).get('idlist',\
          \ [])\n                return {\n                    \"success\": True,\n\
          \                    \"pmids\": pmids,\n                    \"count\": len(pmids),\n\
          \                    \"query\": query\n                }\n            else:\n\
          \                return {\"success\": False, \"error\": f\"HTTP {response.status_code}\"\
          , \"query\": query}\n        except Exception as e:\n            return\
          \ {\"success\": False, \"error\": str(e), \"query\": query}\n\n    # 解析优化后的关键词\n\
          \    keywords = extract_keywords(optimization_keywords)\n\n    search_log\
          \ = f\"\U0001F504 AI优化策略: {keywords['strategy']}\\n\"\n    search_log +=\
          \ f\"\U0001F3AF 优化原因: {keywords['reason']}\\n\"\n    search_log += f\"\U0001F4CA\
          \ 新预期结果: {keywords['expected']}篇文献\\n\\n\"\n\n    all_pmids = []\n    search_attempts\
          \ = []\n\n    # 执行优化后的搜索\n    queries = [\n        (\"优化主查询\", keywords[\"\
          primary\"]),\n        (\"优化备选\", keywords[\"secondary\"]),\n        (\"\
          优化扩展\", keywords[\"broad\"])\n    ]\n\n    for query_type, query in queries:\n\
          \        if not query:\n            continue\n\n        result = search_pubmed(query,\
          \ keywords[\"expected\"])\n        search_attempts.append(f\"{query_type}:\
          \ '{query}' → {result['count']}篇\" if result[\"success\"] else f\"{query_type}:\
          \ '{query}' → 失败\")\n\n        if result[\"success\"]:\n            # 去重添加PMID\n\
          \            for pmid in result[\"pmids\"]:\n                if pmid not\
          \ in all_pmids:\n                    all_pmids.append(pmid)\n\n        \
          \    # 如果优化主查询结果很好，可以停止\n            if query_type == \"优化主查询\" and result[\"\
          count\"] >= keywords[\"expected\"]:\n                search_log += f\"\U0001F389\
          \ {query_type}大幅改善，结果充足\\n\"\n                break\n\n        search_log\
          \ += f\"{'\U0001F389' if result['success'] else '❌'} {query_type}: {result['count']}篇\\\
          n\"\n\n    # 获取优化后的文献详情\n    article_sample = \"\"\n    if all_pmids:\n\
          \        try:\n            pmids_sample = all_pmids[:6]  # 获取更多样本\n    \
          \        summary_params = {\n                'db': 'pubmed',\n         \
          \       'id': ','.join(pmids_sample),\n                'retmode': 'json'\n\
          \            }\n\n            summary_response = requests.get(\n       \
          \         'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi',\n\
          \                params=summary_params,\n                timeout=30\n  \
          \          )\n\n            if summary_response.status_code == 200:\n  \
          \              summary_data = summary_response.json()\n                result_data\
          \ = summary_data.get('result', {})\n\n                details = []\n   \
          \             for pmid in pmids_sample:\n                    if pmid in\
          \ result_data:\n                        article_info = result_data[pmid]\n\
          \                        title = article_info.get('title', '未知标题')[:100]\n\
          \                        journal = article_info.get('fulljournalname', '未知期刊')\n\
          \                        pubdate = article_info.get('pubdate', '未知时间')\n\
          \n                        details.append(f\"PMID:{pmid} | {title}... | {journal}\
          \ | {pubdate}\")\n\n                article_sample = \"\\n\".join(details)\n\
          \        except:\n            article_sample = f\"优化后PMID列表: {', '.join(all_pmids[:10])}\"\
          \n\n    return {\n        \"optimized_success\": \"成功\" if all_pmids else\
          \ \"失败\",\n        \"optimized_total\": str(len(all_pmids)),\n        \"\
          optimization_process\": search_log + \"\\n\".join(search_attempts),\n  \
          \      \"optimized_pmids\": \", \".join(all_pmids[:12]) if all_pmids else\
          \ \"无\",\n        \"optimized_articles\": article_sample if article_sample\
          \ else \"未获取到优化文献详情\",\n        \"optimization_strategy\": keywords[\"strategy\"\
          ],\n        \"optimization_reason\": keywords[\"reason\"]\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          optimization_process:
            children: null
            type: string
          optimization_reason:
            children: null
            type: string
          optimization_strategy:
            children: null
            type: string
          optimized_articles:
            children: null
            type: string
          optimized_pmids:
            children: null
            type: string
          optimized_success:
            children: null
            type: string
          optimized_total:
            children: null
            type: string
        selected: false
        title: 优化搜索执行
        type: code
        variables:
        - value_selector:
          - '3007'
          - text
          variable: optimization_keywords
      height: 52
      id: '3008'
      position:
        x: 1850
        y: 400
      positionAbsolute:
        x: 1850
        y: 400
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.3
          mode: chat
          name: deepseek-r1:32B
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: final_optimized_prompt
          role: system
          text: '# 🎯 AI最终分析报告（经过优化）


            ## 用户问题

            {{#3001.medical_question#}}


            ## AI搜索优化历程

            **经过AI自主优化后成功**


            ### 初次搜索

            - 策略: {{#3003.ai_strategy#}}

            - 结果: {{#3003.total_found#}}篇

            - AI评估: 不满意，需要优化


            ### AI优化过程

            - 优化策略: {{#3008.optimization_strategy#}}

            - 优化原因: {{#3008.optimization_reason#}}

            - 优化过程: {{#3008.optimization_process#}}


            ### 最终结果

            - 优化后结果: {{#3008.optimized_total#}}篇

            - 最终文献: {{#3008.optimized_articles#}}


            ## AI自主分析任务

            基于AI自主优化搜索的完整过程，提供最终的专业分析：


            # 🧠 **AI自主优化成功报告**


            ## 🔄 **AI优化智能化展示**

            - **问题识别**: AI如何识别初次搜索的不足

            - **策略调整**: AI如何制定针对性的优化策略

            - **执行改进**: 展示AI优化执行的效果

            - **迭代能力**: 体现AI的自我学习和改进能力


            ## 📊 **优化效果对比**

            - **数量对比**: 初次{{#3003.total_found#}}篇 → 优化后{{#3008.optimized_total#}}篇

            - **质量提升**: 分析优化后文献质量的改善

            - **相关性增强**: 评估文献与问题匹配度的提升

            - **策略有效性**: 验证AI优化策略的成功


            ## 📚 **最终文献分析**

            - **文献质量**: 基于优化后搜索结果的质量评估

            - **研究价值**: 分析这些文献的学术和临床价值

            - **证据等级**: 评估文献的证据强度和可信度


            ## 💡 **循证医学建议**

            - **主要发现**: 基于最终文献的核心发现

            - **临床指导**: 对临床实践的具体建议

            - **安全要点**: 重要的安全提醒和注意事项

            - **应用建议**: 如何在实践中应用这些证据


            ## 🚀 **AI系统优势展示**

            - **自主优化能力**: 展示AI的自我改进能力

            - **迭代学习**: AI从失败中学习并改进的能力

            - **质量保证**: 通过多轮优化确保结果质量

            - **智能决策**: AI在整个过程中的智能决策展示


            ## 📈 **AI优化价值**

            - **效率提升**: 通过AI优化节省的时间和精力

            - **质量保障**: AI确保搜索结果的高质量

            - **专业性**: AI提供的专业医学分析

            - **持续改进**: AI系统的学习和进步能力


            ## 🔮 **未来展望**

            - **进一步研究方向**: 基于当前结果的研究建议

            - **AI系统改进**: AI系统可以进一步优化的方向

            - **知识更新**: 需要关注的最新进展


            请提供专业、全面、实用的分析报告，特别强调AI自主优化的价值和成果。

            '
        selected: false
        title: 最终分析_优化
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: '3009'
      position:
        x: 2150
        y: 400
      positionAbsolute:
        x: 2150
        y: 400
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '3006'
          - text
          variable: final_result
        - value_selector:
          - '3009'
          - text
          variable: optimized_result
        selected: false
        title: AI分析结果
        type: end
      height: 114
      id: '3010'
      position:
        x: 2450
        y: 300
      positionAbsolute:
        x: 2450
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    viewport:
      x: -168.94343702124206
      y: 311.1040999352194
      zoom: 0.629960504383139
